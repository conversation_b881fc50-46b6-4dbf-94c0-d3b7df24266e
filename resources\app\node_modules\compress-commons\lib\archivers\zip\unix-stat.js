/**
 * node-compress-commons
 *
 * Copyright (c) 2014 <PERSON>, contributors.
 * Licensed under the MIT license.
 * https://github.com/archiverjs/node-compress-commons/blob/master/LICENSE-MIT
 */
module.exports = {
    /**
     * Bits used for permissions (and sticky bit)
     */
    PERM_MASK: 4095, // 07777

    /**
     * Bits used to indicate the filesystem object type.
     */
    FILE_TYPE_FLAG: 61440, // 0170000

    /**
     * Indicates symbolic links.
     */
    LINK_FLAG: 40960, // 0120000

    /**
     * Indicates plain files.
     */
    FILE_FLAG: 32768, // 0100000

    /**
     * Indicates directories.
     */
    DIR_FLAG: 16384, // 040000

    // ----------------------------------------------------------
    // somewhat arbitrary choices that are quite common for shared
    // installations
    // -----------------------------------------------------------

    /**
     * Default permissions for symbolic links.
     */
    DEFAULT_LINK_PERM: 511, // 0777

    /**
     * Default permissions for directories.
     */
    DEFAULT_DIR_PERM: 493, // 0755

    /**
     * Default permissions for plain files.
     */
    DEFAULT_FILE_PERM: 420 // 0644
};